// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

/**
 * @title PeerIdUtils
 * @dev Utility library for handling 39-byte peer IDs efficiently
 * @notice Optimized for minimal gas usage and contract size
 */
library PeerIdUtils {
    /// @dev Standard peer ID length (39 bytes)
    uint256 private constant PEER_ID_LENGTH = 39;
    
    /// @notice Custom error for invalid peer ID length
    error InvalidPeerIdLength();
    
    /**
     * @dev Validates peer ID length and returns its hash
     * @param peerId The peer ID to validate and hash
     * @return peerIdHash The keccak256 hash of the peer ID
     */
    function validateAndHash(bytes calldata peerId) internal pure returns (bytes32 peerIdHash) {
        if (peerId.length != PEER_ID_LENGTH) revert InvalidPeerIdLength();
        peerIdHash = keccak256(peerId);
    }
    
    /**
     * @dev Validates peer ID length (for memory operations)
     * @param peerId The peer ID to validate
     * @return peerIdHash The keccak256 hash of the peer ID
     */
    function validateAndHashMemory(bytes memory peerId) internal pure returns (bytes32 peerIdHash) {
        if (peerId.length != PEER_ID_LENGTH) revert InvalidPeerIdLength();
        peerIdHash = keccak256(peerId);
    }
    
    /**
     * @dev Validates multiple peer IDs and returns their hashes
     * @param peerId1 First peer ID
     * @param peerId2 Second peer ID
     * @return hash1 Hash of first peer ID
     * @return hash2 Hash of second peer ID
     */
    function validateAndHashTwo(
        bytes calldata peerId1, 
        bytes calldata peerId2
    ) internal pure returns (bytes32 hash1, bytes32 hash2) {
        hash1 = validateAndHash(peerId1);
        hash2 = validateAndHash(peerId2);
    }
    
    /**
     * @dev Checks if peer ID is empty (zero-length)
     * @param peerId The peer ID to check
     * @return isEmpty True if peer ID is empty
     */
    function isEmpty(bytes calldata peerId) internal pure returns (bool isEmpty) {
        isEmpty = peerId.length == 0;
    }
}
